function resetHeight() {
    // reset the body height to that of the inner browser
    document.body.style.height = window.innerHeight + "px";
}
// reset the height whenever the window's resized
window.addEventListener("resize", resetHeight);
// called to initially set the height.
resetHeight();


var hid = getUrlParameter('hid');
var doctype = getUrlParameter('doctype');
var initTheme = getUrlParameter('theme');
var frame = document.getElementById("slides-frame");

var showNotes = doctype === 'doc' || getUrlParameter('showNotes') === 'true';
var showCaption = true;
var aspectRatio = 'sixteen10';
var slidesZoom = 100;

var frameBaseUrl = `/view.html?mode=present&hid=${hid}&showNotes=${showNotes}&theme=${initTheme}`;
var warzone = getUrlParameter("warzone");
if (warzone && warzone != 'undefined' && warzone != undefined) {
    frameBaseUrl += `&warzone=${warzone}`;
}
if (doctype && doctype != 'undefined' && doctype != undefined) {
    frameBaseUrl += `&doctype=${doctype}`;

    document.getElementById('articleButton').style.display = 'none';
}

frame.focus();

frame.src = frameBaseUrl;

let state = {};
function setState(st) {
    state = Object.assign(state, st);

    // let playButton = document.getElementById('playButton');
    // if (!state.slidesPlaying || state.paused) {
    //     playButton.innerHTML = '<i class="bi-play icon"></i>';
    // } else {
    //     playButton.innerHTML = '<i class="bi-pause icon"></i>';
    // }

    frame.focus();
}

function togglePlayButton(isShow) {
    let playButton = document.getElementById('playButton');
    playButton.style.display = isShow ? 'block' : 'none';
}

window.addEventListener('message', event => {
    let message = event.data;
    if (typeof message === 'string') {
        try {
            message = JSON.parse(message);
        } catch (err) {
            console.log(err);
        }
    }

    switch (message.type) {
        // case "togglePlayButton":
        //     togglePlayButton(message.value);
        //     break;
        case "pause":
            this.setState({ paused: true });
            break;
        case "resume":
            this.setState({ paused: false });
            break;
        case "ttsSpeak":
            if (!state.paused) {
                speakText(message.text, () => sendMessage({
                    method: 'ttsFinished',
                    params: []
                }));
            }
            break;
        case "ttsCancel":
            cancelSpeech();
            this.setState({ paused: false });
            break;
        case "ttsPause":
            pauseSpeech();
            break;
        case "ttsResume":
            resumeSpeech();
            break;
        case "slidesPlaying":
            this.setState({ slidesPlaying: message.value });
            break;
        case "slidesFinished":
            this.setState({ slidesPlaying: false, paused: false });
            break;
        default:
            break;
    }
});

function sendMessage(message) {
    frame.contentWindow.postMessage(JSON.stringify(message));
}

function openArticle() {
    var url = window.location.href.split("#")[0];

    url = url.replace('present.html', 'view.html');
    url = url + '&mode=article';
    window.open(url);
    frame.focus();
}

function showDisplaySettings() {
    $('#displaySettingsModal').modal('show');
}

let settings = {
    showNotes: showNotes,
    theme: initTheme || 'sky'
}

function setDisplay() {
    // Parse the base URL and existing parameters
    let url = new URL(frameBaseUrl, window.location.origin);

    // Set or update parameters
    url.searchParams.set('theme', settings.theme);
    url.searchParams.set('showNotes', settings.showNotes);

    // Apply frame style if needed
    if (settings.showNotes) {
        frame.style.width = '100%';
        frame.style.height = '100%';
    }

    frame.src = url.toString();
}

function theme_selected(theme_name) {
    settings.theme = theme_name;
    setDisplay();

    document.getElementById('theme_dropdown').innerHTML = theme_name;
}

function setupDisplaySettingsModal() {
    $('#displaySettingsModal').on('show.bs.modal', function (e) {
        document.querySelector(`.btn-group input[id='${settings.showNotes ? 'showNotes' : 'hideNotes'}']`).checked = true;
    });

    $('#displaySettingsModal').on('hidden.bs.modal', function (e) {
        frame.focus();
    });

    $("input[type='radio']").click(function () {
        settings.showNotes = $("input[name='toggleNotes']:checked").val() === 'showNotes';
        setDisplay()
    });

    var theme_dropdown_items = $('#theme_dropdown_items');

    if (theme_dropdown_items.children().length === 0) {
        themes.forEach(theme => {
            theme_dropdown_items.append(`<li><a class="dropdown-item" href="#" onclick="theme_selected('${theme.name}')">${theme.name}</a></li>`)
        })
    }

    document.getElementById('theme_dropdown').innerHTML = themes[0].name;
}

setupDisplaySettingsModal();

i18next.init({
    lng: getLanguage(), // if you're using a language detector, do not define the lng option
    debug: true,
    resources: {
        en: {
            translation: {
                yes: 'Yes',
                no: 'No',
                close: 'Close',
                done: 'Done',

                displaySettings: {
                    title: 'Display Settings',
                    aspectRatio: 'Slides aspect ratio',
                    zoom: 'Zoom',
                    showNotes: 'Show speaker notes',
                    showCaption: 'Show caption',
                    selectTheme: 'Select Theme',
                },
            }
        },
        "zh-CN": {
            translation: {
                yes: '是',
                no: '否',
                close: '关闭',
                done: '完成',

                displaySettings: {
                    title: '显示设置',
                    aspectRatio: '宽高比',
                    zoom: '缩放',
                    showNotes: '显示演讲者笔记',
                    showCaption: '开启字幕',
                    selectTheme: '选择主题',
                },
            }
        }
    }
}, function (err, t) {
    updateContent();
});

function updateContent() {
    localize = locI18next.init(i18next);

    localize('#displaySettingsModal');
}

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>测试图片分离修复</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/reveal.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/theme/white.min.css">
    <style>
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
            max-width: 300px;
        }
    </style>
</head>
<body>
    <div class="reveal">
        <div class="slides">
            <!-- 嵌套 section 测试 -->
            <section>
                <!-- 第一个子页面：只有一张图片 -->
                <section>
                    <h2>页面 1 - 单图片</h2>
                    <p>这个页面应该只显示红色图片。</p>
                    <img src="https://via.placeholder.com/400x300/FF0000/white?text=RED+IMAGE" alt="红色图片">
                </section>
                
                <!-- 第二个子页面：有两张图片 -->
                <section>
                    <h2>页面 2 - 双图片</h2>
                    <p>这个页面应该只显示蓝色和绿色图片。</p>
                    <img src="https://via.placeholder.com/400x300/0000FF/white?text=BLUE+IMAGE" alt="蓝色图片">
                    <img src="https://via.placeholder.com/400x300/00FF00/white?text=GREEN+IMAGE" alt="绿色图片">
                </section>
                
                <!-- 第三个子页面：有图片和列表 -->
                <section>
                    <h2>页面 3 - 图片+列表</h2>
                    <p>这个页面应该只显示黄色图片，并且有列表。</p>
                    <ul>
                        <li>列表项 1</li>
                        <li>列表项 2</li>
                        <li>列表项 3</li>
                    </ul>
                    <img src="https://via.placeholder.com/400x300/FFFF00/black?text=YELLOW+IMAGE" alt="黄色图片">
                </section>
            </section>
            
            <!-- 独立页面测试 -->
            <section>
                <h2>独立页面 - 紫色图片</h2>
                <p>这是独立页面，应该只显示紫色图片。</p>
                <img src="https://via.placeholder.com/400x300/800080/white?text=PURPLE+IMAGE" alt="紫色图片">
            </section>
        </div>
    </div>

    <div class="debug-info" id="debugInfo">
        调试信息将显示在这里
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/reveal.min.js"></script>
    <script src="static-slides/plugin/cardify/plugin.js"></script>
    <script>
        // 调试函数
        function updateDebugInfo() {
            const debugInfo = document.getElementById('debugInfo');
            const currentSlide = Reveal.getCurrentSlide();
            const allImages = document.querySelectorAll('.reveal .slides img');
            const currentSlideImages = currentSlide ? currentSlide.querySelectorAll('img') : [];
            const smartLayoutImages = currentSlide ? currentSlide.querySelectorAll('.smart-layout-images img') : [];

            // 测试 getImageElements 方法
            let pluginImages = [];
            if (currentSlide && window.RevealCardify) {
                pluginImages = RevealCardify.getImageElements(currentSlide);
            }

            let imageInfo = '';
            if (currentSlideImages.length > 0) {
                imageInfo = '<br>当前页面所有图片:<br>';
                currentSlideImages.forEach((img, index) => {
                    const altText = img.alt || `图片${index + 1}`;
                    imageInfo += `- ${altText}<br>`;
                });
            }

            let pluginImageInfo = '';
            if (pluginImages.length > 0) {
                pluginImageInfo = '<br>插件识别的图片:<br>';
                pluginImages.forEach((img, index) => {
                    const altText = img.alt || `图片${index + 1}`;
                    pluginImageInfo += `- ${altText}<br>`;
                });
            }

            let smartLayoutInfo = '';
            if (smartLayoutImages.length > 0) {
                smartLayoutInfo = '<br>SmartLayout 图片:<br>';
                smartLayoutImages.forEach((img, index) => {
                    const altText = img.alt || `图片${index + 1}`;
                    smartLayoutInfo += `- ${altText}<br>`;
                });
            }

            const isProcessed = currentSlide ? (currentSlide.classList.contains('smart-layout-processed') || currentSlide.classList.contains('card-grid-processed')) : false;

            debugInfo.innerHTML = `
                当前幻灯片: ${Reveal.getIndices().h + 1}.${Reveal.getIndices().v + 1}<br>
                已处理: ${isProcessed ? '是' : '否'}<br>
                总图片数: ${allImages.length}<br>
                当前页面图片数: ${currentSlideImages.length}<br>
                插件识别图片数: ${pluginImages.length}<br>
                SmartLayout 图片数: ${smartLayoutImages.length}
                ${imageInfo}
                ${pluginImageInfo}
                ${smartLayoutInfo}
            `;
        }

        // 初始化 Reveal.js
        Reveal.initialize({
            hash: true,
            plugins: [RevealCardify]
        }).then(() => {
            console.log('Reveal.js 初始化完成');
            updateDebugInfo();
            
            // 监听幻灯片变化
            Reveal.on('slidechanged', updateDebugInfo);
            Reveal.on('ready', updateDebugInfo);
        });
    </script>
</body>
</html>

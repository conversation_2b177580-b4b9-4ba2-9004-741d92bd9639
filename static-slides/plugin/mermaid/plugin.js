/**
 * Reveal.js Mermaid Plugin
 * 用于在 Reveal.js 幻灯片中渲染 Mermaid 图表
 */

const RevealMermaid = () => {
  let deck;
  let mermaidInitialized = false;

  // 初始化 Mermaid
  const initMermaid = () => {
    if (typeof mermaid === 'undefined') {
      console.error('Mermaid library not loaded. Please include mermaid.js before this plugin.');
      return false;
    }

    if (!mermaidInitialized) {
      mermaid.initialize({
        startOnLoad: false,
        theme: 'default',
        securityLevel: 'loose',
        flowchart: {
          useMaxWidth: true,
          htmlLabels: true
        },
        sequence: {
          useMaxWidth: true
        },
        gantt: {
          useMaxWidth: true
        }
      });
      mermaidInitialized = true;
    }
    return true;
  };

  // 渲染单个 Mermaid 图表
  const renderMermaidDiagram = async (element, index) => {
    const code = element.textContent.trim();
    
    try {
      // 创建唯一的 ID
      const id = `mermaid-${Date.now()}-${index}`;

      // 渲染图表
      const { svg } = await mermaid.render(id, code);
      
      // 创建容器并插入 SVG
      const container = document.createElement('div');
      container.className = 'mermaid-container';
      container.style.cssText = `
        text-align: center;
        margin: 20px 0;
        overflow: visible;
      `;
      container.innerHTML = svg;
      
      // 替换原始的 code 元素
      element.parentNode.replaceChild(container, element);
      
    } catch (error) {
      console.error('Mermaid rendering error:', error);
      
      // 显示错误信息
      const errorDiv = document.createElement('div');
      errorDiv.className = 'mermaid-error';
      errorDiv.style.cssText = `
        color: #d32f2f;
        background: #ffebee;
        border: 1px solid #f8bbd9;
        border-radius: 4px;
        padding: 10px;
        margin: 10px 0;
        font-family: monospace;
        font-size: 0.9em;
      `;
      errorDiv.textContent = `Mermaid Error: ${error.message}`;
      
      element.parentNode.replaceChild(errorDiv, element);
    }
  };

  // 渲染当前幻灯片中的所有 Mermaid 图表
  const renderCurrentSlide = async () => {

    const currentSlide = deck.getCurrentSlide();

    if (!currentSlide) return;

    // 查找所有未处理的 mermaid 代码块
    const mermaidElements = currentSlide.querySelectorAll('code.language-mermaid:not(.mermaid-processed), .mermaid:not(.mermaid-processed)');
    
    const renderPromises = Array.from(mermaidElements).map(async (element, index) => {
      // 标记为已处理，避免重复渲染
      element.classList.add('mermaid-processed');
      await renderMermaidDiagram(element, index);
    });

    await Promise.all(renderPromises);
  };

  // 渲染所有幻灯片中的 Mermaid 图表（用于打印或导出）
  const renderAllSlides = async () => {
    const slides = deck.getSlides();
    
    for (const slide of slides) {
      const mermaidElements = slide.querySelectorAll('code.language-mermaid:not(.mermaid-processed), .mermaid:not(.mermaid-processed)');
      
      const renderPromises = Array.from(mermaidElements).map(async (element, index) => {
        element.classList.add('mermaid-processed');
        await renderMermaidDiagram(element, index);
      });

      await Promise.all(renderPromises);
    }
  };

  return {
    id: 'mermaid',
    
    init: (reveal) => {
      deck = reveal;
      
      // 初始化 Mermaid
      if (!initMermaid()) {
        return;
      }

      // 添加自定义样式
      const style = document.createElement('style');
      style.textContent = `
        .reveal .mermaid-container {
          font-size: 0.8em;
        }
        
        .reveal .mermaid-container svg {
          max-width: 100%;
          height: auto;
        }
        
        .reveal .mermaid-error {
          font-size: 0.7em;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
          .reveal .mermaid-container {
            font-size: 0.6em;
          }
        }
      `;
      document.head.appendChild(style);

      // 监听幻灯片切换事件
      deck.on('slidechanged', renderCurrentSlide);
      deck.on('ready', renderCurrentSlide);
      
      // 监听打印模式
      deck.on('pdf-ready', renderAllSlides);
    }
  };
};

// 导出插件函数
if (typeof window !== 'undefined') {
  window.RevealMermaid = RevealMermaid;
}

// 导出插件（用于模块化环境）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = RevealMermaid;
}

// AMD 支持
if (typeof define === 'function' && define.amd) {
  define('reveal-mermaid', [], () => RevealMermaid);
}
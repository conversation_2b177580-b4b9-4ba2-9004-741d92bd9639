<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>测试嵌套 Section 修复</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/reveal.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/theme/white.min.css">
    <style>
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="reveal">
        <div class="slides">
            <!-- 父级 section，包含多个子 section -->
            <section>
                <!-- 子 section 1 - 应该被处理 -->
                <section>
                    <h2>幻灯片页面 1</h2>
                    <ul>
                        <li>第一个列表项
                            <ul>
                                <li>嵌套项 1</li>
                                <li>嵌套项 2</li>
                            </ul>
                        </li>
                        <li>第二个列表项</li>
                        <li>第三个列表项</li>
                    </ul>
                </section>
                
                <!-- 子 section 2 - 应该被处理 -->
                <section>
                    <h2>幻灯片页面 2</h2>
                    <p>这是一些文本内容。</p>
                    <img src="https://via.placeholder.com/300x200/4CAF50/white?text=Image+1" alt="测试图片1">
                </section>

                <!-- 子 section 3 - 应该被处理 -->
                <section>
                    <h2>幻灯片页面 3</h2>
                    <p>这个页面有多张图片。</p>
                    <img src="https://via.placeholder.com/300x200/2196F3/white?text=Image+2" alt="测试图片2">
                    <img src="https://via.placeholder.com/300x200/FF9800/white?text=Image+3" alt="测试图片3">
                </section>

                <!-- 子 section 4 - 应该被处理 -->
                <section>
                    <h2>幻灯片页面 4</h2>
                    <ul>
                        <li>简单列表项 1</li>
                        <li>简单列表项 2</li>
                        <li>简单列表项 3</li>
                    </ul>
                </section>
            </section>
            
            <!-- 独立的 section - 应该被处理 -->
            <section>
                <h2>独立幻灯片</h2>
                <p>这是独立的幻灯片，有文本和图片。</p>
                <ol>
                    <li>有序列表项 1
                        <ul>
                            <li>子项 A</li>
                            <li>子项 B</li>
                        </ul>
                    </li>
                    <li>有序列表项 2</li>
                </ol>
                <img src="https://via.placeholder.com/400x250/9C27B0/white?text=Independent+Image" alt="独立图片">
            </section>
        </div>
    </div>

    <div class="debug-info" id="debugInfo">
        调试信息将显示在这里
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/reveal.js/4.3.1/reveal.min.js"></script>
    <script src="static-slides/plugin/cardify/plugin.js"></script>
    <script>
        // 调试函数
        function updateDebugInfo() {
            const debugInfo = document.getElementById('debugInfo');
            const allSections = document.querySelectorAll('.reveal .slides section');
            const leafSections = Array.from(allSections).filter(section => 
                !section.querySelector(':scope > section')
            );
            const processedSections = document.querySelectorAll('.reveal .slides section.smart-layout-processed, .reveal .slides section.card-grid-processed');
            
            debugInfo.innerHTML = `
                总 section 数: ${allSections.length}<br>
                叶子 section 数: ${leafSections.length}<br>
                已处理 section 数: ${processedSections.length}<br>
                当前幻灯片: ${Reveal.getIndices().h + 1}
            `;
        }

        // 初始化 Reveal.js
        Reveal.initialize({
            hash: true,
            plugins: [RevealCardify]
        }).then(() => {
            console.log('Reveal.js 初始化完成');
            updateDebugInfo();
            
            // 监听幻灯片变化
            Reveal.on('slidechanged', updateDebugInfo);
            Reveal.on('ready', updateDebugInfo);
        });
    </script>
</body>
</html>

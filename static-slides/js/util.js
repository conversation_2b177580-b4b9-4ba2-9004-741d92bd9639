function getUrlParameter(sParam, url) {
  console.log('get url parameter............', sParam, url, window.location.search.substring(1))
  var sPageURL = url || window.location.search.substring(1),
    sURLVariables = sPageURL.split('&'),
    sParameterName,
    i;

  for (i = 0; i < sURLVariables.length; i++) {
    sParameterName = sURLVariables[i].split('=');

    if (sParameterName[0] === sParam) {
      // return typeof sParameterName[1] === undefined ? true : decodeURIComponent(sParameterName[1]);
      return sParameterName[1];
    }
  }
  return undefined;
};

function getUrlPageName(url) {
  var path = url || window.location.pathname;
  var page = path.split("/").pop();
  return page;
}

/**
 * Injects the given CSS styles into the DOM.
 *
 * @param {string} value
 */
function createStyleSheet(value) {

  let tag = document.createElement('style');
  tag.type = 'text/css';

  if (value && value.length > 0) {
    if (tag.styleSheet) {
      tag.styleSheet.cssText = value;
    }
    else {
      tag.appendChild(document.createTextNode(value));
    }
  }

  document.head.appendChild(tag);

  return tag;
}

function getLanguage() {
  let lng = getUrlParameter('lng') || navigator.language || window.navigator.language;

  if (lng && lng.toLowerCase().includes('cn')) {
    return 'zh-CN';
  }

  return 'en';
}

const themes = [{
  name: 'black',
  stylesheet: 'dist/theme/black.css',
  desc: 'Black background, white text, blue links'
}, {
  name: 'white',
  stylesheet: 'dist/theme/white.css',
  desc: 'White background, black text, blue links'
}, {
  name: 'league',
  stylesheet: 'dist/theme/league.css',
  desc: 'Gray background, white text, blue links'
}, {
  name: 'beige',
  stylesheet: 'dist/theme/beige.css',
  desc: 'Beige background, dark text, brown links'
}, {
  name: 'sky',
  stylesheet: 'dist/theme/sky.css',
  desc: 'Blue background, thin dark text, blue links'
}, {
  name: 'night',
  stylesheet: 'dist/theme/night.css',
  desc: 'Black background, thick white text, orange links'
}, {
  name: 'serif',
  stylesheet: 'dist/theme/serif.css',
  desc: 'Cappuccino background, gray text, brown links'
}, {
  name: 'simple',
  stylesheet: 'dist/theme/simple.css',
  desc: 'White background, black text, blue links'
}, {
  name: 'solarized',
  stylesheet: 'dist/theme/solarized.css',
  desc: 'Cream-colored background, dark green text, blue links'
}, {
  name: 'blood',
  stylesheet: 'dist/theme/blood.css',
  desc: 'Dark background, thick white text, red links'
}, {
  name: 'moon',
  stylesheet: 'dist/theme/moon.css',
  desc: 'Dark blue background, thick grey text, blue links'
}, {
  name: 'dracula',
  stylesheet: 'dist/theme/dracula.css'
}];

const chart_colors = ['skyblue', 'hotpink', 'green', 'steelblue', 'tomato', 'yellowgreen', 'slateblue', 'purple', 'springgreen', 'gold', 'lightskyblue', 'BlueViolet', 'DarkCyan'];